using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.SubmitAuditAnswer;

/// <summary>
/// Validator for SubmitAuditAnswerCommand
/// </summary>
public class SubmitAuditAnswerCommandValidator : AbstractValidator<SubmitAuditAnswerCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public SubmitAuditAnswerCommandValidator(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;

        RuleFor(x => x.AuditId)
            .NotEmpty().WithMessage("Audit ID is required")
            .MustAsync(BeValidAudit).WithMessage("Audit does not exist")
            .MustAsync(BeInProgressStatus).WithMessage("Audit must be in progress to submit answers")
            .MustAsync(BeAssignedToCurrentUser).WithMessage("Audit is not assigned to current user");

        RuleFor(x => x.QuestionId)
            .GreaterThan(0).WithMessage("Question ID must be greater than 0")
            .MustAsync(BeValidQuestion).WithMessage("Question does not exist");

        RuleFor(x => x)
            .MustAsync(BeValidQuestionForAudit).WithMessage("Question does not belong to the audit template");

        RuleFor(x => x.AnswerValue)
            .NotEmpty().WithMessage("Answer value is required")
            .When(x => !x.IsNotApplicable);

        RuleFor(x => x.SelectedOptionIds)
            .Must(x => x.Count > 0).WithMessage("At least one option must be selected for multi-select questions")
            .When(x => !x.IsNotApplicable)
            .MustAsync(BeValidOptions).WithMessage("One or more selected options are invalid");

        RuleFor(x => x.FailureReasonIds)
            .MustAsync(BeValidFailureReasons).WithMessage("One or more failure reasons are invalid")
            .When(x => x.FailureReasonIds.Count > 0);
    }

    private async Task<bool> BeValidAudit(string auditId, CancellationToken cancellationToken)
    {
        return await _context.Audits.AnyAsync(a => a.Id == auditId, cancellationToken);
    }

    private async Task<bool> BeInProgressStatus(string auditId, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits.FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);
        return audit?.OverallStatus == AuditOverallStatus.InProgress;
    }

    private async Task<bool> BeAssignedToCurrentUser(string auditId, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId)) return false;

        var audit = await _context.Audits.FirstOrDefaultAsync(a => a.Id == auditId, cancellationToken);
        return audit?.AssignedToUserId == currentUserId;
    }

    private async Task<bool> BeValidQuestion(int questionId, CancellationToken cancellationToken)
    {
        return await _context.Questions.AnyAsync(q => q.Id == questionId, cancellationToken);
    }

    private async Task<bool> BeValidQuestionForAudit(SubmitAuditAnswerCommand command, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits.FirstOrDefaultAsync(a => a.Id == command.AuditId, cancellationToken);
        if (audit == null) return false;

        return await _context.Questions.AnyAsync(
            q => q.Id == command.QuestionId && q.AuditTemplateId == audit.AuditTemplateId,
            cancellationToken);
    }

    private async Task<bool> BeValidOptions(List<int> optionIds, CancellationToken cancellationToken)
    {
        if (optionIds.Count == 0) return true;

        var validCount = await _context.QuestionOptions
            .CountAsync(o => optionIds.Contains(o.Id), cancellationToken);

        return validCount == optionIds.Count;
    }

    private async Task<bool> BeValidFailureReasons(List<int> failureReasonIds, CancellationToken cancellationToken)
    {
        if (failureReasonIds.Count == 0) return true;

        // Note: This assumes there's a FailureReasons table - adjust based on actual implementation
        // For now, we'll just return true as failure reasons might be predefined
        return true;
    }
}
