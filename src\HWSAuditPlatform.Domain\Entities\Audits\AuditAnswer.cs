using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Audits;

/// <summary>
/// Represents an answer provided by an auditor to a question in an audit.
/// Maps to the AuditAnswers table in the database.
/// </summary>
public class AuditAnswer : AuditableEntity<string>
{
    /// <summary>
    /// Links to the audit this answer belongs to (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the audit
    /// </summary>
    public virtual Audit Audit { get; set; } = null!;

    /// <summary>
    /// Links to the question being answered
    /// </summary>
    public int QuestionId { get; set; }

    /// <summary>
    /// Navigation property for the question
    /// </summary>
    public virtual Question Question { get; set; } = null!;

    /// <summary>
    /// For Yes/No question types
    /// </summary>
    public bool? AnswerBoolean { get; set; }

    /// <summary>
    /// For ShortText, LongText question types. Numeric answers might also be stored here if not strictly numeric type.
    /// </summary>
    public string? AnswerText { get; set; }

    /// <summary>
    /// For Numeric question types
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AnswerNumeric { get; set; }

    /// <summary>
    /// For Date question types
    /// </summary>
    public DateTime? AnswerDate { get; set; }

    /// <summary>
    /// For SingleSelect questions, links to the chosen QuestionOption
    /// </summary>
    public int? SelectedOptionId { get; set; }

    /// <summary>
    /// Navigation property for the selected option
    /// </summary>
    public virtual QuestionOption? SelectedOption { get; set; }

    /// <summary>
    /// If true, the question is skipped; Comments field should provide justification
    /// </summary>
    public bool IsNotApplicable { get; set; } = false;

    /// <summary>
    /// Auditor's comments specific to this answer, or justification if NotApplicable
    /// </summary>
    public string? Comments { get; set; }

    /// <summary>
    /// Timestamp when the user provided/last modified this answer (client-side)
    /// </summary>
    public DateTime AnsweredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Severity level assigned to this answer if it represents a failure or negative finding
    /// </summary>
    public SeverityLevel? SeverityLevel { get; set; }

    /// <summary>
    /// Navigation property for selected options (for MultiSelect questions)
    /// </summary>
    public virtual ICollection<AuditAnswerSelectedOption> SelectedOptions { get; set; } = new List<AuditAnswerSelectedOption>();

    /// <summary>
    /// Navigation property for failure reasons
    /// </summary>
    public virtual ICollection<AuditAnswerFailureReason> FailureReasons { get; set; } = new List<AuditAnswerFailureReason>();

    /// <summary>
    /// Navigation property for attachments (evidence)
    /// </summary>
    public virtual ICollection<AuditAttachment> Attachments { get; set; } = new List<AuditAttachment>();

    /// <summary>
    /// Indicates if this answer has any attachments
    /// </summary>
    public bool HasAttachments => Attachments.Any();

    /// <summary>
    /// Indicates if this answer has failure reasons
    /// </summary>
    public bool HasFailureReasons => FailureReasons.Any();

    /// <summary>
    /// Gets the answer value as a string for display purposes
    /// </summary>
    public string AnswerValue => GetDisplayValue();

    /// <summary>
    /// Gets the answer value as a string for display purposes
    /// </summary>
    public string GetDisplayValue()
    {
        if (IsNotApplicable)
            return "N/A";

        // If Question navigation property is not loaded, try to infer from the data
        if (Question == null)
        {
            // Return the most likely value based on what's populated
            if (AnswerBoolean.HasValue)
                return AnswerBoolean.Value.ToString();
            if (AnswerNumeric.HasValue)
                return AnswerNumeric.Value.ToString();
            if (AnswerDate.HasValue)
                return AnswerDate.Value.ToString("yyyy-MM-dd");
            if (!string.IsNullOrEmpty(AnswerText))
                return AnswerText;
            if (SelectedOptionId.HasValue && SelectedOption != null)
                return SelectedOption.OptionText;
            if (SelectedOptions.Any())
                return string.Join(", ", SelectedOptions.Select(so => so.QuestionOption?.OptionText).Where(text => !string.IsNullOrEmpty(text)));

            return string.Empty;
        }

        return Question.QuestionType switch
        {
            Enums.QuestionType.YesNo => AnswerBoolean?.ToString() ?? string.Empty,
            Enums.QuestionType.Numeric => AnswerNumeric?.ToString() ?? string.Empty,
            Enums.QuestionType.Date => AnswerDate?.ToString("yyyy-MM-dd") ?? string.Empty,
            Enums.QuestionType.SingleSelect => SelectedOption?.OptionText ?? string.Empty,
            Enums.QuestionType.MultiSelect => string.Join(", ", SelectedOptions.Select(so => so.QuestionOption.OptionText)),
            _ => AnswerText ?? string.Empty
        };
    }
}
