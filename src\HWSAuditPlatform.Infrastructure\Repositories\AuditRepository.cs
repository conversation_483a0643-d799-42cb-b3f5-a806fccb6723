using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Persistence;

namespace HWSAuditPlatform.Infrastructure.Repositories;

/// <summary>
/// Specialized repository for Audit entities with audit-specific operations
/// </summary>
public class AuditRepository : Repository<Audit, string>, IAuditRepository
{
    private readonly ApplicationDbContext _context;

    public AuditRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<IReadOnlyList<Audit>> GetAuditsWithDetailsAsync(
        Expression<Func<Audit, bool>>? predicate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.AssignedToUserGroup)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .Include(a => a.ReviewedByUser)
            .Include(a => a.RecurringAuditSetting)
            .AsQueryable();

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<Audit?> GetAuditWithFullDetailsAsync(string id, CancellationToken cancellationToken = default)
    {
        return await _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.AssignedToUserGroup)
                .ThenInclude(g => g!.UserGroupMembers)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .Include(a => a.ReviewedByUser)
            .Include(a => a.RecurringAuditSetting)
            .Include(a => a.Answers)
                .ThenInclude(aa => aa.Question)
            .Include(a => a.Answers)
                .ThenInclude(aa => aa.SelectedOptions)
                    .ThenInclude(so => so.QuestionOption)
            .Include(a => a.Answers)
                .ThenInclude(aa => aa.FailureReasons)
            .Include(a => a.Answers)
                .ThenInclude(aa => aa.Attachments)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<IReadOnlyList<Audit>> GetAuditsByUserAsync(
        string userId,
        AuditOverallStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Where(a => a.AssignedToUserId == userId);

        if (status.HasValue)
        {
            query = query.Where(a => a.OverallStatus == status.Value);
        }

        return await query
            .OrderByDescending(a => a.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Audit>> GetAuditsByUserGroupAsync(
        string userGroupId,
        AuditOverallStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Where(a => a.AssignedToUserGroupId == userGroupId);

        if (status.HasValue)
        {
            query = query.Where(a => a.OverallStatus == status.Value);
        }

        return await query
            .OrderByDescending(a => a.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Audit>> GetOverdueAuditsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        
        return await _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Where(a => a.DueDate.HasValue && 
                       a.DueDate.Value < now && 
                       a.OverallStatus != AuditOverallStatus.Closed && 
                       a.OverallStatus != AuditOverallStatus.Cancelled)
            .OrderBy(a => a.DueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Audit>> GetAuditsByFactoryAsync(
        int factoryId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Area)
            .Where(a => a.FactoryId == factoryId);

        if (fromDate.HasValue)
        {
            query = query.Where(a => a.ScheduledDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(a => a.ScheduledDate <= toDate.Value);
        }

        return await query
            .OrderByDescending(a => a.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Audit>> GetAuditsByTemplateAsync(
        int templateId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Audits
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Where(a => a.AuditTemplateId == templateId)
            .OrderByDescending(a => a.ScheduledDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetAuditCountByStatusAsync(
        AuditOverallStatus status,
        int? factoryId = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Audits.Where(a => a.OverallStatus == status);

        if (factoryId.HasValue)
        {
            query = query.Where(a => a.FactoryId == factoryId.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task<(IReadOnlyList<Audit> Items, int TotalCount)> GetPagedAuditsWithFiltersAsync(
        int pageNumber,
        int pageSize,
        string? searchTerm = null,
        AuditOverallStatus? status = null,
        string? assignedToUserId = null,
        int? factoryId = null,
        int? areaId = null,
        int? templateId = null,
        DateTime? scheduledDateFrom = null,
        DateTime? scheduledDateTo = null,
        DateTime? dueDateFrom = null,
        DateTime? dueDateTo = null,
        bool? isOverdue = null,
        string sortBy = "ScheduledDate",
        bool ascending = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            query = query.Where(a => 
                a.AuditTemplate.TemplateName.ToLower().Contains(searchTermLower) ||
                (a.AssignedToUser != null && (a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName).ToLower().Contains(searchTermLower)) ||
                a.Factory.FactoryName.ToLower().Contains(searchTermLower) ||
                a.Area.AreaName.ToLower().Contains(searchTermLower));
        }

        if (status.HasValue)
        {
            query = query.Where(a => a.OverallStatus == status.Value);
        }

        if (!string.IsNullOrEmpty(assignedToUserId))
        {
            query = query.Where(a => a.AssignedToUserId == assignedToUserId);
        }

        if (factoryId.HasValue)
        {
            query = query.Where(a => a.FactoryId == factoryId.Value);
        }

        if (areaId.HasValue)
        {
            query = query.Where(a => a.AreaId == areaId.Value);
        }

        if (templateId.HasValue)
        {
            query = query.Where(a => a.AuditTemplateId == templateId.Value);
        }

        if (scheduledDateFrom.HasValue)
        {
            query = query.Where(a => a.ScheduledDate >= scheduledDateFrom.Value);
        }

        if (scheduledDateTo.HasValue)
        {
            query = query.Where(a => a.ScheduledDate <= scheduledDateTo.Value);
        }

        if (dueDateFrom.HasValue)
        {
            query = query.Where(a => a.DueDate >= dueDateFrom.Value);
        }

        if (dueDateTo.HasValue)
        {
            query = query.Where(a => a.DueDate <= dueDateTo.Value);
        }

        if (isOverdue.HasValue && isOverdue.Value)
        {
            var now = DateTime.UtcNow;
            query = query.Where(a => a.DueDate.HasValue && a.DueDate.Value < now && 
                a.OverallStatus != AuditOverallStatus.Closed && 
                a.OverallStatus != AuditOverallStatus.Cancelled);
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = sortBy.ToLower() switch
        {
            "templatename" => ascending 
                ? query.OrderBy(a => a.AuditTemplate.TemplateName)
                : query.OrderByDescending(a => a.AuditTemplate.TemplateName),
            "assigneduser" => ascending
                ? query.OrderBy(a => a.AssignedToUser != null ? a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName : "")
                : query.OrderByDescending(a => a.AssignedToUser != null ? a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName : ""),
            "status" => ascending
                ? query.OrderBy(a => a.OverallStatus)
                : query.OrderByDescending(a => a.OverallStatus),
            "duedate" => ascending
                ? query.OrderBy(a => a.DueDate)
                : query.OrderByDescending(a => a.DueDate),
            "factory" => ascending
                ? query.OrderBy(a => a.Factory.FactoryName)
                : query.OrderByDescending(a => a.Factory.FactoryName),
            _ => ascending
                ? query.OrderBy(a => a.ScheduledDate)
                : query.OrderByDescending(a => a.ScheduledDate)
        };

        // Apply pagination
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }
}
