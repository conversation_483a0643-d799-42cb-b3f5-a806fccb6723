using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.DTOs;

/// <summary>
/// Data Transfer Object for Audit entity
/// </summary>
public class AuditDto : AuditableDto<string>
{
    public int AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignedToUserGroupId { get; set; }
    public string? AssignedToUserGroupName { get; set; }
    public string? AssignedToUserId { get; set; }
    public string? AssignedToUserName { get; set; }
    public DateTime ScheduledDate { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public AuditOverallStatus OverallStatus { get; set; }
    public int FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public int AreaId { get; set; }
    public string? AreaName { get; set; }
    public int? SubAreaId { get; set; }
    public string? SubAreaName { get; set; }
    public decimal? OverallScore { get; set; }
    public string? ManagerComments { get; set; }
    public string? ReviewedByUserId { get; set; }
    public string? ReviewedByUserName { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public string? RecurringAuditSettingId { get; set; }
    
    // Computed properties
    public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.UtcNow && OverallStatus != AuditOverallStatus.Closed && OverallStatus != AuditOverallStatus.Cancelled;
    public bool IsInProgress => OverallStatus == AuditOverallStatus.InProgress;
    public bool CanBeStarted => OverallStatus == AuditOverallStatus.Scheduled;
    public bool CanBeSubmitted => OverallStatus == AuditOverallStatus.InProgress;
}

/// <summary>
/// Simplified Audit DTO for lists and dashboards
/// </summary>
public class AuditSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string? AuditTemplateName { get; set; }
    public string? AssignedToUserName { get; set; }
    public DateTime ScheduledDate { get; set; }
    public DateTime? DueDate { get; set; }
    public AuditOverallStatus OverallStatus { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }
    public decimal? OverallScore { get; set; }
    public bool IsOverdue { get; set; }
}

/// <summary>
/// Data Transfer Object for Audit Answer entity
/// </summary>
public class AuditAnswerDto
{
    public string Id { get; set; } = string.Empty;
    public string AuditId { get; set; } = string.Empty;
    public int QuestionId { get; set; }
    public string? QuestionText { get; set; }
    public QuestionType QuestionType { get; set; }
    public string? AnswerValue { get; set; }
    public bool IsNotApplicable { get; set; }
    public string? Comments { get; set; }
    public SeverityLevel? SeverityLevel { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedByUserId { get; set; }
    public string? UpdatedByUserId { get; set; }

    // Related data
    public List<AuditAnswerSelectedOptionDto> SelectedOptions { get; set; } = new();
    public List<AuditAnswerFailureReasonDto> FailureReasons { get; set; } = new();
    public List<AuditAttachmentDto> Attachments { get; set; } = new();
}

/// <summary>
/// Data Transfer Object for Audit Answer Selected Option entity
/// </summary>
public class AuditAnswerSelectedOptionDto
{
    public string Id { get; set; } = string.Empty;
    public string AuditAnswerId { get; set; } = string.Empty;
    public int QuestionOptionId { get; set; }
    public string? OptionText { get; set; }
    public string? OptionValue { get; set; }
}

/// <summary>
/// Data Transfer Object for Audit Answer Failure Reason entity
/// </summary>
public class AuditAnswerFailureReasonDto
{
    public string Id { get; set; } = string.Empty;
    public string AuditAnswerId { get; set; } = string.Empty;
    public int FailureReasonId { get; set; }
    public string? FailureReasonText { get; set; }
}

/// <summary>
/// Data Transfer Object for Audit Attachment entity
/// </summary>
public class AuditAttachmentDto
{
    public string Id { get; set; } = string.Empty;
    public string AuditAnswerId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string? OriginalFileName { get; set; }
    public string? ContentType { get; set; }
    public long FileSize { get; set; }
    public string? FilePath { get; set; }
    public string? Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? CreatedByUserId { get; set; }
}
