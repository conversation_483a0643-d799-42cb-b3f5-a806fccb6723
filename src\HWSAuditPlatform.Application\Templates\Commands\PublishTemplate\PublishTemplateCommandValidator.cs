using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;

/// <summary>
/// Validator for PublishTemplateCommand
/// </summary>
public class PublishTemplateCommandValidator : AbstractValidator<PublishTemplateCommand>
{
    private readonly IApplicationDbContext _context;

    public PublishTemplateCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.AuditTemplateId)
            .GreaterThan(0).WithMessage("Audit template ID must be greater than 0")
            .MustAsync(BeValidAuditTemplate).WithMessage("Audit template does not exist")
            .MustAsync(NotBeAlreadyPublished).WithMessage("Audit template is already published")
            .MustAsync(HaveAtLeastOneQuestion).WithMessage("Audit template must have at least one question to be published");
    }

    private async Task<bool> BeValidAuditTemplate(int auditTemplateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates.AnyAsync(t => t.Id == auditTemplateId && t.IsActive, cancellationToken);
    }

    private async Task<bool> NotBeAlreadyPublished(int auditTemplateId, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == auditTemplateId, cancellationToken);
        return template != null && !template.IsPublished;
    }

    private async Task<bool> HaveAtLeastOneQuestion(int auditTemplateId, CancellationToken cancellationToken)
    {
        return await _context.Questions.AnyAsync(q => q.AuditTemplateId == auditTemplateId && q.IsActive, cancellationToken);
    }
}
