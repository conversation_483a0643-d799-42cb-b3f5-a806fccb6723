using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Tests.Common;

/// <summary>
/// Factory for creating test database contexts
/// </summary>
public static class TestDbContextFactory
{
    /// <summary>
    /// Creates an in-memory database context for testing
    /// </summary>
    /// <param name="databaseName">Unique database name for isolation</param>
    /// <param name="currentUserService">Optional current user service, defaults to TestCurrentUserService</param>
    /// <returns>Configured ApplicationDbContext</returns>
    public static ApplicationDbContext CreateInMemoryContext(string? databaseName = null, ICurrentUserService? currentUserService = null)
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
            .EnableSensitiveDataLogging()
            .Options;

        // Use provided service or create a default one
        var userService = currentUserService ?? new TestCurrentUserService();
        var context = new ApplicationDbContext(options, userService);

        // Ensure database is created
        context.Database.EnsureCreated();

        return context;
    }

    /// <summary>
    /// Seeds the database with test data
    /// </summary>
    /// <param name="context">Database context to seed</param>
    /// <returns>Task</returns>
    public static async Task SeedTestDataAsync(ApplicationDbContext context)
    {
        // Clear existing data by recreating the database
        await context.Database.EnsureDeletedAsync();
        await context.Database.EnsureCreatedAsync();

        // Get the seeded roles (they should already exist from the configuration)
        var adminRole = await context.Roles.FirstOrDefaultAsync(r => r.RoleName == UserRole.Admin);
        var managerRole = await context.Roles.FirstOrDefaultAsync(r => r.RoleName == UserRole.Manager);
        var auditorRole = await context.Roles.FirstOrDefaultAsync(r => r.RoleName == UserRole.Auditor);

        // If roles don't exist (shouldn't happen with proper seeding), create them
        if (adminRole == null)
        {
            adminRole = new Role
            {
                RoleName = UserRole.Admin,
                Description = "Administrator",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            context.Roles.Add(adminRole);
        }

        if (managerRole == null)
        {
            managerRole = new Role
            {
                RoleName = UserRole.Manager,
                Description = "Manager",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            context.Roles.Add(managerRole);
        }

        if (auditorRole == null)
        {
            auditorRole = new Role
            {
                RoleName = UserRole.Auditor,
                Description = "Auditor",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            context.Roles.Add(auditorRole);
        }

        // Add test location
        var testLocation = new Location
        {
            Id = 1,
            LocationName = "Test Location",            
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.Locations.Add(testLocation);

        // Add test factory
        var testFactory = new Factory
        {
            Id = 1,
            FactoryName = "Test Factory",
            FactoryProcess = "Test Process",
            LocationId = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.Factories.Add(testFactory);

        // Add test users
        var adminUser = User.Create(
            username: "admin",
            firstName: "Admin",
            lastName: "User",
            email: "<EMAIL>",
            roleId: adminRole.Id,
            factoryId: testFactory.Id,
            isActive: true,
            adObjectGuid: Guid.NewGuid().ToString(),
            adDistinguishedName: "CN=Admin User,OU=Users,DC=test,DC=com",
            createdByUserId: "system"
        );

        var managerUser = User.Create(
            username: "manager",
            firstName: "Manager",
            lastName: "User",
            email: "<EMAIL>",
            roleId: managerRole.Id,
            factoryId: testFactory.Id,
            isActive: true,
            adObjectGuid: Guid.NewGuid().ToString(),
            adDistinguishedName: "CN=Manager User,OU=Users,DC=test,DC=com",
            createdByUserId: adminUser.Id
        );

        var auditorUser = User.Create(
            username: "auditor",
            firstName: "Auditor",
            lastName: "User",
            email: "<EMAIL>",
            roleId: auditorRole.Id,
            factoryId: testFactory.Id,
            isActive: true,
            adObjectGuid: Guid.NewGuid().ToString(),
            adDistinguishedName: "CN=Auditor User,OU=Users,DC=test,DC=com",
            createdByUserId: adminUser.Id
        );

        context.Users.AddRange(adminUser, managerUser, auditorUser);

        // Add test areas and subareas
        var testArea = new Area
        {
            Id = 1,
            AreaName = "Test Area",
            FactoryId = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.Areas.Add(testArea);

        var testSubArea = new SubArea
        {
            Id = 1,
            SubAreaName = "Test SubArea",
            AreaId = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        context.SubAreas.Add(testSubArea);

        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Creates a context with seeded test data
    /// </summary>
    /// <param name="databaseName">Unique database name for isolation</param>
    /// <returns>Seeded ApplicationDbContext</returns>
    public static async Task<ApplicationDbContext> CreateSeededContextAsync(string? databaseName = null)
    {
        var context = CreateInMemoryContext(databaseName);
        await SeedTestDataAsync(context);
        return context;
    }
}

/// <summary>
/// Test implementation of ICurrentUserService for testing purposes
/// </summary>
internal class TestCurrentUserService : ICurrentUserService
{
    public string? UserId => "test-user-id";
    public string? Username => "testuser";
    public string? Email => "<EMAIL>";
    public UserRole? Role => UserRole.Admin;
    public int? FactoryId => 1;
    public bool IsAuthenticated => true;

    public bool HasRole(UserRole role) => true;
    public bool HasAnyRole(params UserRole[] roles) => true;
}
