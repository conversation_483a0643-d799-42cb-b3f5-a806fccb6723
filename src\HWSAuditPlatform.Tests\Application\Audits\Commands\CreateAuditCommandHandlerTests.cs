using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Audits.Commands.CreateAudit;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Audits.Commands;

public class CreateAuditCommandHandlerTests : BaseDbTestClass
{
    private readonly CreateAuditCommandHandler _handler;

    public CreateAuditCommandHandlerTests()
    {
        _handler = new CreateAuditCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateAudit()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestAuditTemplateAsync();
        
        var command = new CreateAuditCommand
        {
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            FactoryId = 1,
            AreaId = 1,
            SubAreaId = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        var audit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == result);
        audit.Should().NotBeNull();
        audit!.AuditTemplateId.Should().Be(command.AuditTemplateId);
        audit.AssignmentType.Should().Be(command.AssignmentType);
        audit.AssignedToUserId.Should().Be(command.AssignedToUserId);
        audit.ScheduledDate.Should().Be(command.ScheduledDate);
        audit.DueDate.Should().Be(command.DueDate);
        audit.OverallStatus.Should().Be(AuditOverallStatus.Scheduled);
        audit.FactoryId.Should().Be(command.FactoryId);
        audit.AreaId.Should().Be(command.AreaId);
        audit.SubAreaId.Should().Be(command.SubAreaId);
    }

    [Fact]
    public async Task Handle_WithInvalidTemplate_ShouldThrowException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditCommand
        {
            AuditTemplateId = 999, // Non-existent template
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            FactoryId = 1,
            AreaId = 1
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithUnpublishedTemplate_ShouldThrowException()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestAuditTemplateAsync(isPublished: false);
        
        var command = new CreateAuditCommand
        {
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            FactoryId = 1,
            AreaId = 1
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldGenerateCuidId()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestAuditTemplateAsync();
        
        var command = new CreateAuditCommand
        {
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            FactoryId = 1,
            AreaId = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveLength(25); // CUID length
        result.Should().StartWith("c"); // CUID prefix
    }

    [Fact]
    public async Task Handle_ShouldSetCreatedByUserId()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestAuditTemplateAsync();
        
        var expectedCreatedBy = "test-creator-id";
        MockCurrentUserService.Setup(x => x.UserId).Returns(expectedCreatedBy);
        
        var command = new CreateAuditCommand
        {
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            FactoryId = 1,
            AreaId = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var audit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == result);
        audit.Should().NotBeNull();
        audit!.CreatedByUserId.Should().Be(expectedCreatedBy);
    }

    [Fact]
    public async Task Handle_WithGroupAssignment_ShouldCreateAudit()
    {
        // Arrange
        await SeedTestDataAsync();
        var template = await CreateTestAuditTemplateAsync();
        
        var command = new CreateAuditCommand
        {
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.GroupAny,
            AssignedToUserGroupId = "test-group-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            FactoryId = 1,
            AreaId = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        var audit = await Context.Audits.FirstOrDefaultAsync(a => a.Id == result);
        audit.Should().NotBeNull();
        audit!.AssignmentType.Should().Be(AssignmentType.GroupAny);
        audit.AssignedToUserGroupId.Should().Be(command.AssignedToUserGroupId);
        audit.AssignedToUserId.Should().BeNull();
    }

    private async Task<AuditTemplate> CreateTestAuditTemplateAsync(bool isPublished = true)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Description = "Test Description",
            Version = 1,
            IsPublished = isPublished,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();
        return template;
    }
}
