using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Queries.GetAudit;

/// <summary>
/// Handler for GetAuditQuery
/// </summary>
public class GetAuditQueryHandler : BaseQueryHandler<GetAuditQuery, AuditDto>
{
    private readonly IApplicationDbContext _context;

    public GetAuditQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<AuditDto> Handle(GetAuditQuery request, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits
            .Include(a => a.AuditTemplate)
            .Include(a => a.AssignedToUser)
            .Include(a => a.AssignedToUserGroup)
            .Include(a => a.Factory)
            .Include(a => a.Area)
            .Include(a => a.SubArea)
            .Include(a => a.ReviewedByUser)
            .Include(a => a.RecurringAuditSetting)
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.Id);
        }

        return new AuditDto
        {
            Id = audit.Id,
            AuditTemplateId = audit.AuditTemplateId,
            AuditTemplateName = audit.AuditTemplate.TemplateName,
            AssignmentType = audit.AssignmentType,
            AssignedToUserGroupId = audit.AssignedToUserGroupId,
            AssignedToUserGroupName = audit.AssignedToUserGroup?.GroupName,
            AssignedToUserId = audit.AssignedToUserId,
            AssignedToUserName = audit.AssignedToUser != null 
                ? $"{audit.AssignedToUser.FirstName} {audit.AssignedToUser.LastName}".Trim() 
                : null,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            OverallStatus = audit.OverallStatus,
            FactoryId = audit.FactoryId,
            FactoryName = audit.Factory.FactoryName,
            AreaId = audit.AreaId,
            AreaName = audit.Area.AreaName,
            SubAreaId = audit.SubAreaId,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = audit.OverallScore,
            ManagerComments = audit.ManagerComments,
            ReviewedByUserId = audit.ReviewedByUserId,
            ReviewedByUserName = audit.ReviewedByUser != null 
                ? $"{audit.ReviewedByUser.FirstName} {audit.ReviewedByUser.LastName}".Trim() 
                : null,
            ReviewedAt = audit.ReviewedAt,
            RecurringAuditSettingId = audit.RecurringAuditSettingId,
            CreatedAt = audit.CreatedAt,
            UpdatedAt = audit.UpdatedAt,
            CreatedByUserId = audit.CreatedByUserId,
            UpdatedByUserId = audit.UpdatedByUserId,
            RecordVersion = audit.RecordVersion
        };
    }
}
