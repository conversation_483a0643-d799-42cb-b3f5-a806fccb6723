using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Represents a user in the audit platform.
/// Maps to the Users table in the database.
/// </summary>
public class User : AuditableEntity<string>, IAggregateRoot
{
    /// <summary>
    /// Login username for the user
    /// </summary>
    [Required]
    [MaxLength(256)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    [MaxLength(100)]
    public string? FirstName { get; set; }

    /// <summary>
    /// User's last name
    /// </summary>
    [MaxLength(100)]
    public string? LastName { get; set; }

    /// <summary>
    /// User's email address, used for notifications and potentially login
    /// </summary>
    [Required]
    [MaxLength(256)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Foreign key linking to the user's primary role
    /// </summary>
    public int RoleId { get; set; }

    /// <summary>
    /// Navigation property for the user's role
    /// </summary>
    public virtual Role Role { get; set; } = null!;

    /// <summary>
    /// Optional: Primary factory association for the user
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Navigation property for the user's primary factory
    /// </summary>
    public virtual Factory? Factory { get; set; }

    /// <summary>
    /// Indicates if the user account is active or disabled
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Timestamp of the user's last successful login
    /// </summary>
    public DateTime? LastLoginDate { get; set; }

    /// <summary>
    /// AD objectGUID for synced users (required - all users are AD synced)
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string AdObjectGuid { get; set; } = string.Empty;

    /// <summary>
    /// Full AD distinguished name (optional, for debugging/tracing)
    /// </summary>
    public string? AdDistinguishedName { get; set; }

    /// <summary>
    /// Last time this user was synced from AD
    /// </summary>
    public DateTime? AdSyncLastDate { get; set; }

    /// <summary>
    /// Navigation property for user group memberships
    /// </summary>
    public virtual ICollection<UserGroupMember> UserGroupMemberships { get; set; } = new List<UserGroupMember>();

    /// <summary>
    /// Navigation property for user group memberships (EF Core relationship)
    /// </summary>
    public virtual ICollection<UserGroupMember> UserGroupMembers { get; set; } = new List<UserGroupMember>();

    /// <summary>
    /// Gets the user's full name
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Indicates if this user is synced from Active Directory (always true - all users are AD synced)
    /// </summary>
    public bool IsAdSynced => true;

    /// <summary>
    /// Creates a new user instance
    /// </summary>
    /// <param name="username">Username</param>
    /// <param name="firstName">First name</param>
    /// <param name="lastName">Last name</param>
    /// <param name="email">Email address</param>
    /// <param name="roleId">Role ID</param>
    /// <param name="factoryId">Factory ID (optional)</param>
    /// <param name="isActive">Active status</param>
    /// <param name="adObjectGuid">AD Object GUID</param>
    /// <param name="adDistinguishedName">AD Distinguished Name</param>
    /// <param name="createdByUserId">ID of user creating this user</param>
    /// <returns>New user instance</returns>
    public static User Create(
        string username,
        string? firstName,
        string? lastName,
        string email,
        int roleId,
        int? factoryId,
        bool isActive,
        string? adObjectGuid,
        string? adDistinguishedName,
        string? createdByUserId)
    {
        return new User
        {
            Id = CuidGenerator.Generate(),
            Username = username,
            FirstName = firstName??"",
            LastName = lastName?? "",
            Email = email,
            RoleId = roleId,
            FactoryId = factoryId,
            IsActive = isActive,
            AdObjectGuid = adObjectGuid ?? string.Empty,
            AdDistinguishedName = adDistinguishedName,
            CreatedByUserId = createdByUserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Updates the user's information
    /// </summary>
    /// <param name="firstName">First name</param>
    /// <param name="lastName">Last name</param>
    /// <param name="email">Email address</param>
    /// <param name="roleId">Role ID</param>
    /// <param name="factoryId">Factory ID (optional)</param>
    /// <param name="isActive">Active status</param>
    public void Update(string? firstName, string? lastName, string email, int roleId, int? factoryId, bool isActive)
    {
        FirstName = firstName;
        LastName = lastName;
        Email = email;
        RoleId = roleId;
        FactoryId = factoryId;
        IsActive = isActive;
        UpdatedAt = DateTime.UtcNow;
    }


}
