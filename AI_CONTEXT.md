# HWS Audit Platform - AI Context Document

## System Overview

The **HWS Audit Platform** is a comprehensive manufacturing quality audit management system built with .NET 9, implementing Clean Architecture principles with Domain-Driven Design (DDD). The platform supports offline-capable Progressive Web Apps (PWA), Active Directory integration, and enterprise-scale audit workflows.

### Key Architectural Goals

- **Clean Architecture**: Maintainable, testable, loosely coupled design
- **Offline-First**: PWA capabilities with data synchronization
- **Enterprise Integration**: Active Directory, local file storage, monitoring
- **Scalability**: Multi-tenant, high-volume usage support
- **Security**: Role-based access, audit trails, data protection

## Project Structure

```
HWSAuditPlatform/
├── src/
│   ├── HWSAuditPlatform.Domain/          # Business entities and rules
│   ├── HWSAuditPlatform.Application/     # Use cases and orchestration (CQRS)
│   ├── HWSAuditPlatform.Infrastructure/  # External concerns implementation
│   ├── HWSAuditPlatform.ApiService/      # Web API controllers
│   ├── HWSAuditPlatform.Web/            # Blazor web application
│   ├── HWSAuditPlatform.AppHost/        # .NET Aspire orchestration
│   ├── HWSAuditPlatform.ServiceDefaults/ # Shared service configuration
│   ├── HWSAuditPlatform.SchedulerWorker/ # Background services
│   └── HWSAuditPlatform.Tests/          # Unit and integration tests
├── docs/                                 # Documentation
└── scripts/                             # Deployment and utility scripts
```

## Architecture Layers

### 1. Domain Layer (`src/HWSAuditPlatform.Domain/`)

**Purpose**: Core business logic, entities, and domain rules

**Key Components**:

- **Entities**: 20+ domain entities across 7 functional areas
- **Enumerations**: 11 strongly-typed enums mapping to database types
- **Value Objects**: Immutable objects for complex domain concepts
- **Domain Events**: Event-driven architecture support
- **Business Rules**: Comprehensive validation and workflow enforcement

**Directory Structure**:

```
Domain/
├── Common/                     # Base classes (BaseEntity, AuditableEntity, IAggregateRoot)
├── Entities/                   # Domain entities by functional area
│   ├── Users/                 # User management (5 entities)
│   ├── Organization/          # Hierarchical structure (4 entities)
│   ├── Templates/             # Audit templates (4 entities)
│   ├── Audits/               # Audit execution (5 entities)
│   ├── Findings/             # Non-conformity management (2 entities)
│   ├── Scheduling/           # Recurring audits (2 entities)
│   └── Workflow/             # Correction requests (2 entities)
├── Enums/                     # Domain enumerations
├── ValueObjects/              # Value objects
├── Events/                    # Domain events
└── Exceptions/                # Domain-specific exceptions
```

**Key Entities**:

- **User**: AD-synced users with roles and factory assignments
- **Factory**: Manufacturing sites with hierarchical organization
- **AuditTemplate**: Reusable audit blueprints
- **Audit**: Audit execution instances with status workflow
- **Finding**: Non-conformities with corrective actions
- **RecurringAuditSetting**: Automated audit scheduling

### 2. Application Layer (`src/HWSAuditPlatform.Application/`)

**Purpose**: Use case orchestration using CQRS pattern with MediatR

**Key Technologies**:

- **MediatR**: CQRS implementation
- **AutoMapper**: Object-to-object mapping
- **FluentValidation**: Input validation
- **Pipeline Behaviors**: Cross-cutting concerns

**Directory Structure**:

```
Application/
├── Common/                     # Base classes and shared utilities
├── Interfaces/                 # Application interfaces
├── Users/                     # User management use cases
├── Organization/              # Organizational structure management
├── Audits/                    # Audit execution functionality
├── Templates/                 # Audit template management
├── Findings/                  # Finding and corrective action management
├── Scheduling/                # Recurring audit scheduling
├── Workflow/                  # Correction requests and workflow
├── Sync/                      # PWA synchronization logic
└── DependencyInjection.cs     # Service registration
```

### 3. Infrastructure Layer (`src/HWSAuditPlatform.Infrastructure/`)

**Purpose**: External concerns implementation

**Key Technologies**:

- **Entity Framework Core**: ORM and database access
- **SQL Server**: Primary database
- **Local File Storage**: Attachment storage (no Azure dependencies)
- **System.DirectoryServices**: Active Directory integration

**Key Features**:

- **ApplicationDbContext**: EF Core with automatic audit tracking
- **Repository Pattern**: Generic repository with CRUD and pagination
- **Unit of Work**: Transaction management
- **Identity Services**: User context and authentication

### 4. API Service Layer (`src/HWSAuditPlatform.ApiService/`)

**Purpose**: RESTful API with JWT authentication

**Key Features**:

- **JWT Authentication**: Token-based with 8-hour expiration
- **Role-based Authorization**: Admin, Manager, Auditor roles
- **Scalar UI**: API documentation (preferred over Swagger UI)
- **Health Checks**: System monitoring endpoints

## Database Architecture

### Primary Database (AuditFlowDB)

**Schema File**: `docs/database.dbml`

**Key Table Groups**:

- **Core Access & Users**: Roles, Users, UserGroups, UserGroupMembers, AdGroupRoleMapping
- **Organizational Structure**: Location, Factories, Areas, SubAreas
- **Audit Definition**: AuditTemplates, QuestionGroups, Questions, QuestionOptions
- **Audit Execution**: Audits, AuditAnswers, AuditAttachments
- **Findings Management**: Findings, CorrectiveActions
- **Scheduling**: RecurringAuditSettings, RecurrenceRules
- **Workflow**: AuditCorrectionRequests, AuditLogs

### Support Database (Application Runtime)

**Schema File**: `docs/suppor_database.dbml`

**Key Tables**:

- **UserApiKeys**: API key management
- **UserRefreshTokens**: JWT refresh token storage
- **StoredEvents**: Event sourcing support
- **ApplicationCache**: Database-backed caching
- **FeatureFlags**: Feature toggle management
- **TelemetryEvents**: Usage analytics and diagnostics

### Primary Key Strategies

- **CUID**: For offline-created entities (Audit, AuditAnswer, User)
- **Integer**: For server-managed entities (AuditTemplate, Question)

## Domain Model Details

### User Management

**Entities**: User, Role, UserGroup, UserGroupMember, AdGroupRoleMapping

**Key Features**:

- All users are AD-synced (no offline registration)
- CUID primary keys for offline support
- Role-based permissions (Admin, Manager, Auditor)
- Factory-based access control

**User Entity Properties**:

```csharp
public class User : AuditableEntity<string>, IAggregateRoot
{
    public string Username { get; set; }           // Required, unique
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string Email { get; set; }              // Required, unique
    public int RoleId { get; set; }                // FK to Roles
    public int? FactoryId { get; set; }            // Optional factory assignment
    public bool IsActive { get; set; } = true;
    public bool IsAdSynced { get; set; } = true;
    public string AdObjectGuid { get; set; }       // Required for AD sync
    public string? AdDistinguishedName { get; set; }
    public DateTime? AdSyncLastDate { get; set; }
    public string FullName => $"{FirstName} {LastName}".Trim();
}
```

### Organization Hierarchy

**Structure**: Location → Factory → Area → SubArea

**Entities**:

- **Location**: Geographical locations (countries, regions)
- **Factory**: Manufacturing sites with process information
- **Area**: Departments within factories
- **SubArea**: Granular divisions within areas

### Audit Templates

**Entities**: AuditTemplate, QuestionGroup, Question, QuestionOption

**Question Types**:

- YesNo, Numeric, ShortText, LongText, Date, SingleSelect, MultiSelect

**Key Features**:

- Conditional logic support
- Evidence requirements per question
- Template versioning and publishing workflow

### Audit Execution

**Entities**: Audit, AuditAnswer, AuditAttachment, AuditAnswerSelectedOption, AuditAnswerFailureReason

**Audit Status Workflow**:

- Draft → InProgress → PendingReview → Completed → Archived

**Key Features**:

- CUID primary keys for offline creation
- Evidence capture with file attachments
- Multi-select answer support
- Failure reason tracking

## Authentication & Authorization

### JWT Configuration

```json
{
  "Jwt": {
    "Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "HWSAuditPlatform",
    "Audience": "HWSAuditPlatformUsers"
  }
}
```

### JWT Claims Structure

```csharp
var claims = new[]
{
    new Claim(ClaimTypes.NameIdentifier, userId),    // User CUID
    new Claim(ClaimTypes.Name, username),
    new Claim(ClaimTypes.Email, email),
    new Claim(ClaimTypes.Role, role),                // Admin/Manager/Auditor
    new Claim("FactoryId", factoryId),
    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
    new Claim(JwtRegisteredClaimNames.Iat, timestamp)
};
```

### Authorization Policies

- **AuditorOrAbove**: Auditor, Manager, or Admin roles
- **ManagerOrAbove**: Manager or Admin roles only
- **AdminOnly**: Admin role only
- **FactoryAccess**: Users can only access their assigned factory data

## API Endpoints Structure

### Base URL Pattern

`/api/v1/{resource}`

### Core Endpoints

```
Authentication:
POST   /api/v1/auth/login          # AD authentication
POST   /api/v1/auth/refresh        # Token refresh
GET    /api/v1/auth/me            # Current user info

Users:
GET    /api/v1/users              # List users (paginated)
GET    /api/v1/users/{id}         # Get user by ID
POST   /api/v1/users              # Create user
PUT    /api/v1/users/{id}         # Update user
DELETE /api/v1/users/{id}         # Soft delete user

Audits:
GET    /api/v1/audits             # List audits (paginated)
GET    /api/v1/audits/{id}        # Get audit details
GET    /api/v1/audits/my-audits   # Current user's audits
POST   /api/v1/audits             # Create audit
POST   /api/v1/audits/{id}/start  # Start audit execution
POST   /api/v1/audits/{id}/submit # Submit for review

Organization:
GET    /api/v1/organization/locations
GET    /api/v1/organization/factories
GET    /api/v1/organization/areas
GET    /api/v1/organization/subareas

Health:
GET    /health                    # Basic health check
GET    /health/info              # API information
GET    /health/ping              # Connectivity test
```

## File Storage Configuration

### Local File Storage (No Azure Dependencies)

```json
{
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  }
}
```

## Testing Structure

### Test Organization

```
src/HWSAuditPlatform.Tests/
├── Domain/                        # Domain layer tests
│   ├── Common/                   # Base class tests
│   ├── Entities/                 # Entity tests by functional area
│   ├── ValueObjects/             # Value object tests
│   ├── Events/                   # Domain event tests
│   └── Exceptions/               # Exception tests
├── Application/                   # Application layer tests
├── Infrastructure/                # Infrastructure tests
└── Integration/                   # Integration tests
    └── Controllers/              # API controller tests
```

### Test Coverage Goals

- **Entities**: 90%+ coverage of business logic
- **Value Objects**: 100% coverage
- **Domain Events**: 100% coverage
- **Exceptions**: 100% coverage

### Running Tests

```bash
# All domain tests
dotnet test --filter "FullyQualifiedName~HWSAuditPlatform.Tests.Domain"

# Specific test class
dotnet test --filter "FullyQualifiedName~UserTests"

# With coverage
dotnet test --collect:"XPlat Code Coverage"
```

## Development Guidelines

### Naming Conventions

- **Entities**: PascalCase, singular nouns
- **Properties**: PascalCase
- **Methods**: PascalCase, verb-noun pattern
- **Constants**: UPPER_SNAKE_CASE
- **Private fields**: _camelCase with underscore prefix

### File Naming

- **Avoid naming custom File classes 'File'** to prevent conflicts with System.IO.File
- **Use alternatives**: StoredFile, UploadedFile, FileInfo

### Package Management

- **Always use package managers** (dotnet add package) instead of manually editing .csproj files
- **Never manually edit package configuration files**

### Database Migrations

```bash
# Create migration
dotnet ef migrations add MigrationName --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Update database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

## Business Rules Summary

### User Management

- All users must be AD-synced
- Users can only access their assigned factory data
- Role changes require admin privileges
- Soft deletion maintains referential integrity

### Audit Workflow

- Only published templates can be used for new audits
- Audits follow strict status transitions: Draft → InProgress → PendingReview → Completed
- Evidence is required for questions marked as requiring evidence
- Users can only access audits assigned to them or their factory

### Template Management

- Templates must be published before use
- Question conditional logic prevents circular references
- Evidence types are restricted to allowed MIME types
- Template versioning maintains audit history integrity

### Finding Management

- Findings must be linked to specific audit answers
- Corrective actions require valid assignees and due dates
- Status transitions follow CAPA workflow requirements
- Manager approval required for correction requests

## Infrastructure Constraints

### Technology Stack

- **.NET 8**: Primary framework
- **SQL Server**: Database (LocalDB for development)
- **Entity Framework Core**: ORM
- **Local File Storage**: No Azure dependencies
- **Active Directory**: User authentication only
- **Scalar UI**: API documentation (preferred over Swagger UI)

### Security Requirements

- **JWT tokens**: 8-hour expiration
- **HTTPS only**: All API communications
- **Role-based access**: Strict authorization policies
- **Audit trails**: All changes tracked with user and timestamp
- **Data protection**: Sensitive data handling compliance

## Key Configuration Files

### API Service Configuration (`appsettings.json`)

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb;Trusted_Connection=true"
  },
  "Jwt": {
    "Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "HWSAuditPlatform",
    "Audience": "HWSAuditPlatformUsers"
  },
  "FileStorage": {
    "Type": "Local"
  }
}
```

## Common Development Patterns

### CQRS Implementation

```csharp
// Command
public class CreateUserCommand : BaseCommand<string>
{
    public string Username { get; set; }
    public string Email { get; set; }
    // ... other properties
}

// Handler
public class CreateUserCommandHandler : BaseCommandHandler<CreateUserCommand, string>
{
    public override async Task<string> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        var user = User.Create(/* parameters */);
        await _context.Users.AddAsync(user, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
        return user.Id;
    }
}
```

### Repository Pattern

```csharp
public interface IRepository<TEntity, TKey> where TEntity : BaseEntity<TKey>
{
    Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default);
    Task<IEnumerable<TEntity>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default);
    Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default);
    Task DeleteAsync(TKey id, CancellationToken cancellationToken = default);
}
```

### Domain Events

```csharp
public class UserCreatedEvent : IDomainEvent
{
    public string UserId { get; }
    public string Username { get; }
    public DateTime OccurredOn { get; }

    public UserCreatedEvent(string userId, string username)
    {
        UserId = userId;
        Username = username;
        OccurredOn = DateTime.UtcNow;
    }
}
```

## Domain Enumerations

### Core Enums

```csharp
public enum AuditStatus
{
    Draft = 0,
    InProgress = 1,
    PendingReview = 2,
    Completed = 3,
    Archived = 4
}

public enum QuestionType
{
    YesNo = 0,
    Numeric = 1,
    ShortText = 2,
    LongText = 3,
    Date = 4,
    SingleSelect = 5,
    MultiSelect = 6
}

public enum FindingStatus
{
    Open = 0,
    InProgress = 1,
    PendingVerification = 2,
    Closed = 3,
    Cancelled = 4
}

public enum UserRole
{
    Auditor = 1,
    Manager = 2,
    Admin = 3
}
```

## Entity Relationships Summary

### User Relationships

- User (1) ←→ (N) UserGroupMember
- User (N) ←→ (1) Role
- User (N) ←→ (1) Factory (optional)

### Organization Relationships

- Location (1) ←→ (N) Factory
- Factory (1) ←→ (N) Area
- Area (1) ←→ (N) SubArea

### Audit Relationships

- AuditTemplate (1) ←→ (N) Audit
- Audit (1) ←→ (N) AuditAnswer
- AuditAnswer (1) ←→ (N) AuditAttachment
- Question (1) ←→ (N) AuditAnswer

### Template Relationships

- AuditTemplate (1) ←→ (N) QuestionGroup
- QuestionGroup (1) ←→ (N) Question
- Question (1) ←→ (N) QuestionOption

## API Response Patterns

### Standard Response Format

```csharp
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public T? Data { get; set; }
    public List<string>? Errors { get; set; }
    public DateTime Timestamp { get; set; }
}
```

### Error Response Format

```csharp
public class ApiErrorResponse
{
    public bool Success { get; set; } = false;
    public string Message { get; set; }
    public List<string> Errors { get; set; }
    public string? TraceId { get; set; }
    public DateTime Timestamp { get; set; }
}
```

## Validation Patterns

### FluentValidation Example

```csharp
public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    public CreateUserCommandValidator()
    {
        RuleFor(x => x.Username)
            .NotEmpty().WithMessage("Username is required")
            .MaximumLength(256).WithMessage("Username cannot exceed 256 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(256).WithMessage("Email cannot exceed 256 characters");
    }
}
```

## Logging Patterns

### Structured Logging

```csharp
Logger.LogInformation("User {Username} created audit {AuditId} at {Timestamp}",
    username, auditId, DateTime.UtcNow);

Logger.LogWarning("Failed login attempt for username: {Username} from IP: {IPAddress}",
    username, ipAddress);

Logger.LogError(exception, "Error processing audit {AuditId} for user {UserId}",
    auditId, userId);
```

## Performance Considerations

### Database Optimization

- **Indexes**: Optimized for common query patterns
- **Pagination**: All list endpoints support pagination
- **Lazy Loading**: Disabled to prevent N+1 queries
- **Query Optimization**: Use Include() for related data

### Caching Strategy

- **Application Cache**: Database-backed for distributed scenarios
- **Memory Cache**: For frequently accessed reference data
- **Cache Invalidation**: Tag-based invalidation strategies

## Security Best Practices

### Input Validation

- **All inputs validated** using FluentValidation
- **SQL injection prevention** through parameterized queries
- **XSS protection** through proper encoding
- **File upload validation** with MIME type checking

### Authentication Security

- **JWT tokens**: Secure key management
- **Token expiration**: 8-hour maximum lifetime
- **Refresh tokens**: Secure rotation strategy
- **Password policies**: Enforced through AD

This document provides comprehensive context for AI-assisted development of the HWS Audit Platform, covering architecture, domain model, API structure, database design, and development guidelines.
