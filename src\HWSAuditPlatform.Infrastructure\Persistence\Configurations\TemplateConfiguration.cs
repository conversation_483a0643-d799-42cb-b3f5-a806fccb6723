using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for AuditTemplate entity
/// </summary>
public class AuditTemplateConfiguration : IEntityTypeConfiguration<AuditTemplate>
{
    public void Configure(EntityTypeBuilder<AuditTemplate> builder)
    {
        builder.ToTable("AuditTemplates");

        // Primary Key
        builder.HasKey(at => at.Id);

        // Properties
        builder.Property(at => at.TemplateName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(at => at.Description)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(at => at.Version)
            .IsRequired();

        builder.Property(at => at.IsPublished)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(at => at.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Ignore computed properties - they are calculated in C# code
        // Note: For SQL Server, these could be computed columns, but for in-memory testing we ignore them
        builder.Ignore(at => at.FullName);
        builder.Ignore(at => at.CanBeUsed);

        // Auditable properties
        builder.Property(at => at.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(at => at.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(at => at.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(at => at.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(at => at.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(at => at.TemplateName)
            .HasDatabaseName("IX_AuditTemplates_TemplateName");

        builder.HasIndex(at => new { at.TemplateName, at.Version })
            .IsUnique()
            .HasDatabaseName("IX_AuditTemplates_TemplateName_Version");

        builder.HasIndex(at => at.IsPublished)
            .HasDatabaseName("IX_AuditTemplates_IsPublished");

        builder.HasIndex(at => at.IsActive)
            .HasDatabaseName("IX_AuditTemplates_IsActive");

        // Ignore domain events
        builder.Ignore(at => at.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for QuestionGroup entity
/// </summary>
public class QuestionGroupConfiguration : IEntityTypeConfiguration<QuestionGroup>
{
    public void Configure(EntityTypeBuilder<QuestionGroup> builder)
    {
        builder.ToTable("QuestionGroups");

        // Primary Key
        builder.HasKey(qg => qg.Id);

        // Properties
        builder.Property(qg => qg.AuditTemplateId)
            .IsRequired();

        builder.Property(qg => qg.GroupName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(qg => qg.Description)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(qg => qg.DisplayOrder)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(qg => qg.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Auditable properties
        builder.Property(qg => qg.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(qg => qg.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(qg => qg.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(qg => qg.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(qg => qg.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(qg => qg.AuditTemplateId)
            .HasDatabaseName("IX_QuestionGroups_AuditTemplateId");

        builder.HasIndex(qg => new { qg.AuditTemplateId, qg.DisplayOrder })
            .HasDatabaseName("IX_QuestionGroups_AuditTemplateId_DisplayOrder");

        builder.HasIndex(qg => qg.GroupName)
            .HasDatabaseName("IX_QuestionGroups_GroupName");

        // Relationships
        builder.HasOne(qg => qg.AuditTemplate)
            .WithMany(at => at.QuestionGroups)
            .HasForeignKey(qg => qg.AuditTemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(qg => qg.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for Question entity
/// </summary>
public class QuestionConfiguration : IEntityTypeConfiguration<Question>
{
    public void Configure(EntityTypeBuilder<Question> builder)
    {
        builder.ToTable("Questions");

        // Primary Key
        builder.HasKey(q => q.Id);

        // Properties
        builder.Property(q => q.AuditTemplateId)
            .IsRequired();

        builder.Property(q => q.QuestionGroupId)
            .IsRequired(false);

        builder.Property(q => q.QuestionText)
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(q => q.QuestionType)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(q => q.DisplayOrder)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(q => q.IsRequired)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(q => q.Weight)
            .HasColumnType("decimal(7,2)")
            .IsRequired(false);

        builder.Property(q => q.HelpText)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(q => q.ParentQuestionId)
            .IsRequired(false);

        builder.Property(q => q.TriggerAnswerValue)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(q => q.SeverityLevel)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired(false);

        builder.Property(q => q.EvidenceRequired)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(q => q.AllowedEvidenceTypes)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(q => q.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Ignore computed properties - they are calculated in C# code
        // Note: For SQL Server, this could be a computed column, but for in-memory testing we ignore it
        builder.Ignore(q => q.IsConditional);

        // Auditable properties
        builder.Property(q => q.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(q => q.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(q => q.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(q => q.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(q => q.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(q => q.AuditTemplateId)
            .HasDatabaseName("IX_Questions_AuditTemplateId");

        builder.HasIndex(q => q.QuestionGroupId)
            .HasDatabaseName("IX_Questions_QuestionGroupId");

        builder.HasIndex(q => new { q.AuditTemplateId, q.DisplayOrder })
            .HasDatabaseName("IX_Questions_AuditTemplateId_DisplayOrder");

        builder.HasIndex(q => q.ParentQuestionId)
            .HasDatabaseName("IX_Questions_ParentQuestionId");

        builder.HasIndex(q => q.QuestionType)
            .HasDatabaseName("IX_Questions_QuestionType");

        // Relationships
        builder.HasOne(q => q.AuditTemplate)
            .WithMany(at => at.Questions)
            .HasForeignKey(q => q.AuditTemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(q => q.QuestionGroup)
            .WithMany(qg => qg.Questions)
            .HasForeignKey(q => q.QuestionGroupId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(q => q.ParentQuestion)
            .WithMany(pq => pq.ChildQuestions)
            .HasForeignKey(q => q.ParentQuestionId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        // Ignore domain events
        builder.Ignore(q => q.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for QuestionOption entity
/// </summary>
public class QuestionOptionConfiguration : IEntityTypeConfiguration<QuestionOption>
{
    public void Configure(EntityTypeBuilder<QuestionOption> builder)
    {
        builder.ToTable("QuestionOptions");

        // Primary Key
        builder.HasKey(qo => qo.Id);

        // Properties
        builder.Property(qo => qo.QuestionId)
            .IsRequired();

        builder.Property(qo => qo.OptionText)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(qo => qo.OptionValue)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(qo => qo.DisplayOrder)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(qo => qo.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Auditable properties
        builder.Property(qo => qo.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(qo => qo.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(qo => qo.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(qo => qo.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(qo => qo.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(qo => qo.QuestionId)
            .HasDatabaseName("IX_QuestionOptions_QuestionId");

        builder.HasIndex(qo => new { qo.QuestionId, qo.DisplayOrder })
            .HasDatabaseName("IX_QuestionOptions_QuestionId_DisplayOrder");

        builder.HasIndex(qo => qo.OptionText)
            .HasDatabaseName("IX_QuestionOptions_OptionText");

        // Relationships
        builder.HasOne(qo => qo.Question)
            .WithMany(q => q.Options)
            .HasForeignKey(qo => qo.QuestionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(qo => qo.DomainEvents);
    }
}
