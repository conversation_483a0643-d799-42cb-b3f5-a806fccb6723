using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.CreateAudit;

/// <summary>
/// Handler for CreateAuditCommand
/// </summary>
public class CreateAuditCommandHandler : BaseCommandHandler<CreateAuditCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateAuditCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<string> Handle(CreateAuditCommand request, CancellationToken cancellationToken)
    {
        // Get the audit template to ensure it exists and is published
        var auditTemplate = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId && t.IsPublished && t.IsActive, 
                cancellationToken);

        if (auditTemplate == null)
        {
            throw new InvalidOperationException($"Audit template with ID {request.AuditTemplateId} not found or not published");
        }

        // Create the audit entity
        var audit = new Audit
        {
            Id = CuidGenerator.Generate(),
            AuditTemplateId = request.AuditTemplateId,
            AssignmentType = request.AssignmentType,
            AssignedToUserGroupId = request.AssignedToUserGroupId,
            AssignedToUserId = request.AssignedToUserId,
            ScheduledDate = request.ScheduledDate,
            DueDate = request.DueDate,
            OverallStatus = AuditOverallStatus.Scheduled,
            FactoryId = request.FactoryId,
            AreaId = request.AreaId,
            SubAreaId = request.SubAreaId,
            CreatedByUserId = _currentUserService.UserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Add to context and save
        await _context.Audits.AddAsync(audit, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return audit.Id;
    }


}
