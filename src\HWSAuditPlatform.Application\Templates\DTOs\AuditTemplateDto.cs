using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Templates.DTOs;

/// <summary>
/// Data Transfer Object for Audit Template entity
/// </summary>
public class AuditTemplateDto : AuditableDto<int>
{
    public string TemplateName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Version { get; set; }
    public bool IsPublished { get; set; }
    public bool IsActive { get; set; }
    public string FullName { get; set; } = string.Empty;
    public bool CanBeUsed { get; set; }
    
    // Related data
    public List<QuestionGroupDto> QuestionGroups { get; set; } = new();
    public List<QuestionDto> Questions { get; set; } = new();
}

/// <summary>
/// Simplified Audit Template DTO for lists
/// </summary>
public class AuditTemplateSummaryDto
{
    public int Id { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Version { get; set; }
    public bool IsPublished { get; set; }
    public bool IsActive { get; set; }
    public string FullName { get; set; } = string.Empty;
    public bool CanBeUsed { get; set; }
    public int QuestionCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Data Transfer Object for Question Group entity
/// </summary>
public class QuestionGroupDto
{
    public int Id { get; set; }
    public int AuditTemplateId { get; set; }
    public string GroupName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    
    // Related data
    public List<QuestionDto> Questions { get; set; } = new();
}

/// <summary>
/// Data Transfer Object for Question entity
/// </summary>
public class QuestionDto
{
    public int Id { get; set; }
    public int AuditTemplateId { get; set; }
    public int? QuestionGroupId { get; set; }
    public string? QuestionGroupName { get; set; }
    public string QuestionText { get; set; } = string.Empty;
    public QuestionType QuestionType { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsRequired { get; set; }
    public decimal? Weight { get; set; }
    public string? HelpText { get; set; }
    public int? ParentQuestionId { get; set; }
    public string? TriggerAnswerValue { get; set; }
    public SeverityLevel? SeverityLevel { get; set; }
    public bool EvidenceRequired { get; set; }
    public string? AllowedEvidenceTypes { get; set; }
    public bool IsActive { get; set; }
    public bool IsConditional { get; set; }
    
    // Related data
    public List<QuestionOptionDto> Options { get; set; } = new();
    public List<QuestionDto> ChildQuestions { get; set; } = new();
}

/// <summary>
/// Data Transfer Object for Question Option entity
/// </summary>
public class QuestionOptionDto
{
    public int Id { get; set; }
    public int QuestionId { get; set; }
    public string OptionText { get; set; } = string.Empty;
    public string? OptionValue { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
}
