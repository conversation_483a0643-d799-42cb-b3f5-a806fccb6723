using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Audits.Commands.CreateAudit;
using HWSAuditPlatform.Application.Audits.Commands.StartAudit;
using HWSAuditPlatform.Application.Audits.Commands.SubmitAudit;
using HWSAuditPlatform.Application.Audits.Commands.ReviewAudit;
using HWSAuditPlatform.Application.Audits.Queries.GetAudit;
using HWSAuditPlatform.Application.Audits.Queries.GetAudits;
using HWSAuditPlatform.Application.Audits.Queries.GetMyAudits;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Request model for audit review operations
/// </summary>
public class ReviewAuditRequest
{
    /// <summary>
    /// Whether the audit is approved or rejected
    /// </summary>
    public bool Approved { get; set; }

    /// <summary>
    /// Optional comments from the reviewer
    /// </summary>
    public string? Comments { get; set; }
}

/// <summary>
/// Request model for audit submission operations
/// </summary>
public class SubmitAuditRequest
{
    /// <summary>
    /// Optional comments from the auditor
    /// </summary>
    public string? AuditorComments { get; set; }
}

/// <summary>
/// Controller for audit management operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class AuditsController : BaseController
{
    public AuditsController(IMediator mediator, ILogger<AuditsController> logger) 
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Get a paginated list of audits
    /// </summary>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="searchTerm">Search term</param>
    /// <param name="status">Audit status filter</param>
    /// <param name="factoryId">Factory ID filter</param>
    /// <param name="assignedToUserId">Assigned user ID filter</param>
    /// <param name="areaId">Area ID filter</param>
    /// <param name="templateId">Template ID filter</param>
    /// <param name="scheduledDateFrom">Scheduled date from filter</param>
    /// <param name="scheduledDateTo">Scheduled date to filter</param>
    /// <param name="dueDateFrom">Due date from filter</param>
    /// <param name="dueDateTo">Due date to filter</param>
    /// <param name="isOverdue">Overdue filter</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audits</returns>
    [HttpGet]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<AuditSummaryDto>>> GetAudits(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? status = null,
        [FromQuery] int? factoryId = null,
        [FromQuery] string? assignedToUserId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] int? templateId = null,
        [FromQuery] DateTime? scheduledDateFrom = null,
        [FromQuery] DateTime? scheduledDateTo = null,
        [FromQuery] DateTime? dueDateFrom = null,
        [FromQuery] DateTime? dueDateTo = null,
        [FromQuery] bool? isOverdue = null,
        [FromQuery] string sortBy = "ScheduledDate",
        [FromQuery] string sortDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting audits - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

        // For non-admin users, filter by their factory
        var currentUserFactoryId = GetCurrentUserFactoryId();
        if (!HasAnyRole("Admin") && currentUserFactoryId.HasValue)
        {
            factoryId = currentUserFactoryId.Value;
        }

        // Parse status enum if provided
        AuditOverallStatus? statusEnum = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var parsedStatus))
        {
            statusEnum = parsedStatus;
        }

        var query = new GetAuditsQuery
        {
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50),
            SearchTerm = searchTerm,
            Status = statusEnum,
            FactoryId = factoryId,
            AssignedToUserId = assignedToUserId,
            AreaId = areaId,
            AuditTemplateId = templateId,
            ScheduledDateFrom = scheduledDateFrom,
            ScheduledDateTo = scheduledDateTo,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            IsOverdue = isOverdue,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific audit by ID
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audit details</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(AuditDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AuditDto>> GetAudit(string id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting audit with ID: {AuditId}", id);

        var query = new GetAuditQuery(id);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Create a new audit
    /// </summary>
    /// <param name="command">Audit creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created audit ID</returns>
    [HttpPost]
    [Authorize(Policy = "ManagerOrAdmin")]
    [ProducesResponseType(typeof(string), 201)]
    public async Task<ActionResult<string>> CreateAudit(
        CreateAuditCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating audit for template: {TemplateId}", command.AuditTemplateId);
        var auditId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetAudit), new { id = auditId }, auditId, "Audit created successfully");
    }

    /// <summary>
    /// Get audits assigned to the current user
    /// </summary>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="status">Status filter</param>
    /// <param name="factoryId">Factory ID filter</param>
    /// <param name="areaId">Area ID filter</param>
    /// <param name="isOverdue">Overdue filter</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of assigned audits</returns>
    [HttpGet("my-audits")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<AuditSummaryDto>>> GetMyAudits(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] int? factoryId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] bool? isOverdue = null,
        [FromQuery] string sortBy = "ScheduledDate",
        [FromQuery] string sortDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        var currentUserId = GetCurrentUserId();
        if (string.IsNullOrEmpty(currentUserId))
        {
            return Unauthorized();
        }

        Logger.LogInformation("Getting audits for user: {UserId}", currentUserId);

        // Parse status enum if provided
        AuditOverallStatus? statusEnum = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var parsedStatus))
        {
            statusEnum = parsedStatus;
        }

        var query = new GetMyAuditsQuery
        {
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50),
            Status = statusEnum,
            FactoryId = factoryId,
            AreaId = areaId,
            IsOverdue = isOverdue,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Start an audit
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/start")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> StartAudit(string id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Starting audit: {AuditId}", id);

        var command = new StartAuditCommand { AuditId = id };
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Audit started successfully");
    }

    /// <summary>
    /// Submit an audit for review
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="request">Submit audit request with optional comments</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/submit")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> SubmitAudit(
        string id,
        [FromBody] SubmitAuditRequest? request = null,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Submitting audit: {AuditId}", id);

        var command = new SubmitAuditCommand
        {
            AuditId = id,
            AuditorComments = request?.AuditorComments
        };
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Audit submitted successfully");
    }

    /// <summary>
    /// Review and approve/reject an audit
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="request">Review request containing approval status and comments</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/review")]
    [Authorize(Policy = "ManagerOrAdmin")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> ReviewAudit(
        string id,
        [FromBody] ReviewAuditRequest request,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Reviewing audit: {AuditId}, Approved: {Approved}", id, request.Approved);

        var command = new ReviewAuditCommand
        {
            AuditId = id,
            Approved = request.Approved,
            Comments = request.Comments
        };
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess($"Audit {(request.Approved ? "approved" : "rejected")} successfully");
    }
}
