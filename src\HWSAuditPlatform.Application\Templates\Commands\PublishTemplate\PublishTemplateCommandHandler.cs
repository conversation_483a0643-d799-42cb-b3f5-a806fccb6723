using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Commands.PublishTemplate;

/// <summary>
/// Handler for PublishTemplateCommand
/// </summary>
public class PublishTemplateCommandHandler : BaseCommandHandler<PublishTemplateCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public PublishTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(PublishTemplateCommand request, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId, cancellationToken);

        if (template == null || !template.IsActive)
        {
            throw new NotFoundException(nameof(Domain.Entities.Templates.AuditTemplate), request.AuditTemplateId);
        }

        if (template.IsPublished)
        {
            throw new InvalidOperationException("Template is already published");
        }

        // Verify template has at least one question
        var hasQuestions = await _context.Questions
            .AnyAsync(q => q.AuditTemplateId == request.AuditTemplateId && q.IsActive, cancellationToken);

        if (!hasQuestions)
        {
            throw new InvalidOperationException("Cannot publish template without questions");
        }

        // Publish the template
        template.IsPublished = true;
        template.UpdatedAt = DateTime.UtcNow;
        template.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);
    }
}
