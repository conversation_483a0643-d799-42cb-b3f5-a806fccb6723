using FluentAssertions;
using HWSAuditPlatform.Application.Audits.Queries.GetAudit;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Audits.Queries;

public class GetAuditQueryHandlerTests : BaseDbTestClass
{
    private readonly GetAuditQueryHandler _handler;

    public GetAuditQueryHandlerTests()
    {
        _handler = new GetAuditQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithValidId_ShouldReturnAudit()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(audit.Id);
        result.AuditTemplateId.Should().Be(audit.AuditTemplateId);
        result.AuditTemplateName.Should().Be("Test Template");
        result.AssignmentType.Should().Be(audit.AssignmentType);
        result.AssignedToUserId.Should().Be(audit.AssignedToUserId);
        result.ScheduledDate.Should().Be(audit.ScheduledDate);
        result.DueDate.Should().Be(audit.DueDate);
        result.OverallStatus.Should().Be(audit.OverallStatus);
        result.FactoryId.Should().Be(audit.FactoryId);
        result.FactoryName.Should().Be("Test Factory");
        result.AreaId.Should().Be(audit.AreaId);
        result.AreaName.Should().Be("Test Area");
        result.SubAreaId.Should().Be(audit.SubAreaId);
    }

    [Fact]
    public async Task Handle_WithNonExistentId_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetAuditQuery("non-existent-id");

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithGroupAssignment_ShouldReturnGroupInfo()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(assignmentType: AssignmentType.GroupAny);
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.AssignmentType.Should().Be(AssignmentType.GroupAny);
        result.AssignedToUserGroupId.Should().Be("test-group-id");
        result.AssignedToUserId.Should().BeNull();
    }

    [Fact]
    public async Task Handle_WithCompletedAudit_ShouldReturnCompletionDetails()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(
            status: AuditOverallStatus.Closed,
            completedAt: DateTime.UtcNow.AddDays(-1),
            overallScore: 85.5m);
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.OverallStatus.Should().Be(AuditOverallStatus.Closed);
        result.CompletedAt.Should().NotBeNull();
        result.OverallScore.Should().Be(85.5m);
    }

    [Fact]
    public async Task Handle_WithStartedAudit_ShouldReturnStartTime()
    {
        // Arrange
        await SeedTestDataAsync();
        var startTime = DateTime.UtcNow.AddHours(-2);
        var audit = await CreateTestAuditAsync(
            status: AuditOverallStatus.InProgress,
            startedAt: startTime);
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.OverallStatus.Should().Be(AuditOverallStatus.InProgress);
        result.StartedAt.Should().Be(startTime);
    }

    [Fact]
    public async Task Handle_WithReviewedAudit_ShouldReturnReviewDetails()
    {
        // Arrange
        await SeedTestDataAsync();
        var reviewTime = DateTime.UtcNow.AddDays(-1);
        var audit = await CreateTestAuditAsync(
            status: AuditOverallStatus.Closed,
            reviewedAt: reviewTime,
            reviewedByUserId: "reviewer-id");
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.ReviewedAt.Should().Be(reviewTime);
        result.ReviewedByUserId.Should().Be("reviewer-id");
    }

    [Fact]
    public async Task Handle_WithManagerComments_ShouldReturnComments()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync(managerComments: "Excellent work on this audit");
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.ManagerComments.Should().Be("Excellent work on this audit");
    }

    [Fact]
    public async Task Handle_ShouldReturnAuditTrailFields()
    {
        // Arrange
        await SeedTestDataAsync();
        var audit = await CreateTestAuditAsync();
        
        var query = new GetAuditQuery(audit.Id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.CreatedAt.Should().Be(audit.CreatedAt);
        result.UpdatedAt.Should().Be(audit.UpdatedAt);
        result.CreatedByUserId.Should().Be(audit.CreatedByUserId);
        result.UpdatedByUserId.Should().Be(audit.UpdatedByUserId);
        result.RecordVersion.Should().Be(audit.RecordVersion);
    }

    private async Task<Audit> CreateTestAuditAsync(
        AuditOverallStatus status = AuditOverallStatus.Scheduled,
        AssignmentType assignmentType = AssignmentType.Individual,
        DateTime? startedAt = null,
        DateTime? completedAt = null,
        DateTime? reviewedAt = null,
        string? reviewedByUserId = null,
        decimal? overallScore = null,
        string? managerComments = null)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = assignmentType,
            AssignedToUserId = assignmentType == AssignmentType.Individual ? "test-user-id" : null,
            AssignedToUserGroupId = assignmentType == AssignmentType.GroupAny ? "test-group-id" : null,
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            StartedAt = startedAt,
            CompletedAt = completedAt,
            ReviewedAt = reviewedAt,
            ReviewedByUserId = reviewedByUserId,
            OverallStatus = status,
            OverallScore = overallScore,
            ManagerComments = managerComments,
            FactoryId = 1,
            AreaId = 1,
            SubAreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedByUserId = "creator-id",
            UpdatedByUserId = "updater-id",
            RecordVersion = 1
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();
        return audit;
    }
}
