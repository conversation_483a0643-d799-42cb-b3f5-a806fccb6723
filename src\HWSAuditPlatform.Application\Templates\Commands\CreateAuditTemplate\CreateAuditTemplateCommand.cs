using HWSAuditPlatform.Application.Common;

namespace HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;

/// <summary>
/// Command to create a new audit template
/// </summary>
public class CreateAuditTemplateCommand : BaseCommand<int>
{
    /// <summary>
    /// Name of the audit template
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the template's purpose and scope
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Design version of the template
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Whether to publish the template immediately
    /// </summary>
    public bool IsPublished { get; set; } = false;
}
