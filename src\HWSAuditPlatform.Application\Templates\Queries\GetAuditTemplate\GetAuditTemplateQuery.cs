using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Templates.DTOs;

namespace HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;

/// <summary>
/// Query to get a single audit template with full details
/// </summary>
public class GetAuditTemplateQuery : BaseQuery<AuditTemplateDto>
{
    /// <summary>
    /// The ID of the audit template to retrieve
    /// </summary>
    public int Id { get; set; }

    public GetAuditTemplateQuery(int id)
    {
        Id = id;
    }

    public GetAuditTemplateQuery() { }
}
