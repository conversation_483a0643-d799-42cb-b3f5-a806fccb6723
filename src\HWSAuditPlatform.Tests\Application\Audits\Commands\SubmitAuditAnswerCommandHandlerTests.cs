using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Audits.Commands.SubmitAuditAnswer;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Tests.Application.Audits.Commands;

public class SubmitAuditAnswerCommandHandlerTests : BaseDbTestClass
{
    private readonly SubmitAuditAnswerCommandHandler _handler;

    public SubmitAuditAnswerCommandHandlerTests()
    {
        _handler = new SubmitAuditAnswerCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateAuditAnswer()
    {
        // Arrange
        await SeedTestDataAsync();
        var (audit, question) = await CreateTestAuditWithQuestionAsync();
        
        var command = new SubmitAuditAnswerCommand
        {
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerValue = "Yes",
            Comments = "Test comment",
            IsNotApplicable = false
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        var answer = await Context.AuditAnswers.FirstOrDefaultAsync(a => a.Id == result);
        answer.Should().NotBeNull();
        answer!.AuditId.Should().Be(command.AuditId);
        answer.QuestionId.Should().Be(command.QuestionId);
        answer.AnswerValue.Should().Be(command.AnswerValue);
        answer.Comments.Should().Be(command.Comments);
        answer.IsNotApplicable.Should().Be(command.IsNotApplicable);
    }

    [Fact]
    public async Task Handle_WithExistingAnswer_ShouldUpdateAnswer()
    {
        // Arrange
        await SeedTestDataAsync();
        var (audit, question) = await CreateTestAuditWithQuestionAsync();
        
        // Create existing answer
        var existingAnswer = new AuditAnswer
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerBoolean = false,
            Comments = "Old comment",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        
        Context.AuditAnswers.Add(existingAnswer);
        await Context.SaveChangesAsync();
        
        var command = new SubmitAuditAnswerCommand
        {
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerValue = "Yes",
            Comments = "Updated comment",
            IsNotApplicable = false
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().Be(existingAnswer.Id);
        
        var updatedAnswer = await Context.AuditAnswers.FirstOrDefaultAsync(a => a.Id == result);
        updatedAnswer.Should().NotBeNull();
        updatedAnswer!.AnswerValue.Should().Be("Yes");
        updatedAnswer.Comments.Should().Be("Updated comment");
    }

    [Fact]
    public async Task Handle_WithSelectedOptions_ShouldCreateSelectedOptions()
    {
        // Arrange
        await SeedTestDataAsync();
        var (audit, question) = await CreateTestAuditWithQuestionAsync(QuestionType.MultiSelect);
        
        var option1 = new QuestionOption
        {
            Id = 1,
            QuestionId = question.Id,
            OptionText = "Option 1",
            DisplayOrder = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
        
        var option2 = new QuestionOption
        {
            Id = 2,
            QuestionId = question.Id,
            OptionText = "Option 2",
            DisplayOrder = 2,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
        
        Context.QuestionOptions.AddRange(option1, option2);
        await Context.SaveChangesAsync();
        
        var command = new SubmitAuditAnswerCommand
        {
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerValue = "Multiple options selected",
            SelectedOptionIds = new List<int> { option1.Id, option2.Id }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var selectedOptions = await Context.AuditAnswerSelectedOptions
            .Where(so => so.AuditAnswerId == result)
            .ToListAsync();
            
        selectedOptions.Should().HaveCount(2);
        selectedOptions.Should().Contain(so => so.QuestionOptionId == option1.Id);
        selectedOptions.Should().Contain(so => so.QuestionOptionId == option2.Id);
    }

    [Fact]
    public async Task Handle_WithNonExistentAudit_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new SubmitAuditAnswerCommand
        {
            AuditId = "non-existent-id",
            QuestionId = 1,
            AnswerValue = "Yes"
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithNonExistentQuestion_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        var (audit, _) = await CreateTestAuditWithQuestionAsync();
        
        var command = new SubmitAuditAnswerCommand
        {
            AuditId = audit.Id,
            QuestionId = 999, // Non-existent question
            AnswerValue = "Yes"
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldSetCreatedByUserId()
    {
        // Arrange
        await SeedTestDataAsync();
        var currentUserId = "test-current-user";
        MockCurrentUserService.Setup(x => x.UserId).Returns(currentUserId);
        
        var (audit, question) = await CreateTestAuditWithQuestionAsync();
        
        var command = new SubmitAuditAnswerCommand
        {
            AuditId = audit.Id,
            QuestionId = question.Id,
            AnswerValue = "Yes"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var answer = await Context.AuditAnswers.FirstOrDefaultAsync(a => a.Id == result);
        answer.Should().NotBeNull();
        answer!.CreatedByUserId.Should().Be(currentUserId);
    }

    [Fact]
    public async Task Handle_WithNotApplicable_ShouldSetNotApplicableFlag()
    {
        // Arrange
        await SeedTestDataAsync();
        var (audit, question) = await CreateTestAuditWithQuestionAsync();
        
        var command = new SubmitAuditAnswerCommand
        {
            AuditId = audit.Id,
            QuestionId = question.Id,
            IsNotApplicable = true,
            Comments = "Not applicable for this area"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var answer = await Context.AuditAnswers.FirstOrDefaultAsync(a => a.Id == result);
        answer.Should().NotBeNull();
        answer!.IsNotApplicable.Should().BeTrue();
        answer.AnswerValue.Should().Be("N/A");
        answer.Comments.Should().Be("Not applicable for this area");
    }

    private async Task<(Audit audit, Question question)> CreateTestAuditWithQuestionAsync(
        QuestionType questionType = QuestionType.YesNo)
    {
        var template = new AuditTemplate
        {
            TemplateName = "Test Template",
            Version = 1,
            IsPublished = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.AuditTemplates.Add(template);
        await Context.SaveChangesAsync();

        var question = new Question
        {
            Id = 1,
            AuditTemplateId = template.Id,
            QuestionText = "Test Question",
            QuestionType = questionType,
            DisplayOrder = 1,
            IsRequired = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Questions.Add(question);
        await Context.SaveChangesAsync();

        var audit = new Audit
        {
            Id = $"c{Guid.NewGuid():N}"[..25],
            AuditTemplateId = template.Id,
            AssignmentType = AssignmentType.Individual,
            AssignedToUserId = "test-user-id",
            ScheduledDate = DateTime.UtcNow.AddDays(1),
            DueDate = DateTime.UtcNow.AddDays(7),
            OverallStatus = AuditOverallStatus.InProgress,
            FactoryId = 1,
            AreaId = 1,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        Context.Audits.Add(audit);
        await Context.SaveChangesAsync();

        return (audit, question);
    }
}
