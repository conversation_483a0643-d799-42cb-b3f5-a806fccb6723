using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Templates;

namespace HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;

/// <summary>
/// Handler for CreateAuditTemplateCommand
/// </summary>
public class CreateAuditTemplateCommandHandler : BaseCommandHandler<CreateAuditTemplateCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateAuditTemplateCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<int> Handle(CreateAuditTemplateCommand request, CancellationToken cancellationToken)
    {
        var auditTemplate = new AuditTemplate
        {
            TemplateName = request.TemplateName,
            Description = request.Description,
            Version = request.Version,
            IsPublished = request.IsPublished,
            IsActive = true,
            CreatedByUserId = _currentUserService.UserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _context.AuditTemplates.AddAsync(auditTemplate, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return auditTemplate.Id;
    }
}
