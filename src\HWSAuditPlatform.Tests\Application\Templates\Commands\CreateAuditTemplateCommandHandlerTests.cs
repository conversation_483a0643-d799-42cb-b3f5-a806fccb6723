using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Templates.Commands.CreateAuditTemplate;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.Templates.Commands;

public class CreateAuditTemplateCommandHandlerTests : BaseDbTestClass
{
    private readonly CreateAuditTemplateCommandHandler _handler;

    public CreateAuditTemplateCommandHandlerTests()
    {
        _handler = new CreateAuditTemplateCommandHandler(Context, MockCurrentUserService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Safety Audit Template",
            Description = "Comprehensive safety audit for manufacturing facilities",
            Version = 1,
            IsPublished = false
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeGreaterThan(0);
        
        var template = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == result);
        template.Should().NotBeNull();
        template!.TemplateName.Should().Be(command.TemplateName);
        template.Description.Should().Be(command.Description);
        template.Version.Should().Be(command.Version);
        template.IsPublished.Should().Be(command.IsPublished);
        template.IsActive.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithPublishedFlag_ShouldCreatePublishedTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Quality Audit Template",
            Description = "Quality control audit template",
            Version = 1,
            IsPublished = true
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var template = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == result);
        template.Should().NotBeNull();
        template!.IsPublished.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldSetCreatedByUserId()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var expectedCreatedBy = "test-creator-id";
        MockCurrentUserService.Setup(x => x.UserId).Returns(expectedCreatedBy);
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Test Template",
            Version = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var template = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == result);
        template.Should().NotBeNull();
        template!.CreatedByUserId.Should().Be(expectedCreatedBy);
    }

    [Fact]
    public async Task Handle_ShouldSetAuditFields()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var beforeCreate = DateTime.UtcNow;
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Test Template",
            Version = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var template = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == result);
        template.Should().NotBeNull();
        template!.CreatedAt.Should().BeAfter(beforeCreate);
        template.UpdatedAt.Should().BeAfter(beforeCreate);
    }

    [Fact]
    public async Task Handle_WithNullDescription_ShouldCreateTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Simple Template",
            Description = null,
            Version = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeGreaterThan(0);
        
        var template = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == result);
        template.Should().NotBeNull();
        template!.Description.Should().BeNull();
    }

    [Fact]
    public async Task Handle_WithHigherVersion_ShouldCreateTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Versioned Template",
            Version = 2
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var template = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == result);
        template.Should().NotBeNull();
        template!.Version.Should().Be(2);
    }

    [Fact]
    public async Task Handle_WithLongDescription_ShouldCreateTemplate()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var longDescription = new string('A', 1500); // Long but within limits
        
        var command = new CreateAuditTemplateCommand
        {
            TemplateName = "Detailed Template",
            Description = longDescription,
            Version = 1
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var template = await Context.AuditTemplates.FirstOrDefaultAsync(t => t.Id == result);
        template.Should().NotBeNull();
        template!.Description.Should().Be(longDescription);
    }
}
